import type React from "react";
import { useCallback, useEffect, useRef, useState } from "react";
import type { TableCellBorder, TableProperties } from "@/types/table";
import type { ColorInfo } from "@/utils/apiService";
import { createLogicalGrid } from "@/utils/tableUtils";
import { ColorPicker } from "./ColorPicker";
import { Input } from "./input";
import { Label } from "./label";

// Helper function to convert px to pt
const pxToPt = (px: number): number => {
	return Math.round(px * 0.75 * 10) / 10; // 1px = 0.75pt, round to 1 decimal place
};

interface BorderSelectionControlProps {
	tableProperties: TableProperties;
	onUpdateCellBorders: (
		selection: {
			start: { row: number; col: number };
			end: { row: number; col: number };
		},
		activeLines: ActiveLines,
		borderWidthPt: number | null,
		borderColorHex: string | null,
	) => void;
	selectedElementId: string | undefined | null;
	apiColors: ColorInfo[];
	isLoadingColors: boolean;
	colorError: string | null;
}

interface ActiveLines {
	outerTop: boolean;
	outerRight: boolean;
	outerBottom: boolean;
	outerLeft: boolean;
	innerHorizontal: boolean;
	innerVertical: boolean;
}

const initialActiveLines: ActiveLines = {
	outerTop: false,
	outerRight: false,
	outerBottom: false,
	outerLeft: false,
	innerHorizontal: false,
	innerVertical: false,
};

export const BorderSelectionControl: React.FC<BorderSelectionControlProps> = ({
	tableProperties,
	onUpdateCellBorders,
	selectedElementId,
	apiColors,
	isLoadingColors,
	colorError,
}) => {
	const {
		selection,
		cells: tableCells,
		borderColor: globalBorderColor,
	} = tableProperties;
	const [activeLines, setActiveLines] =
		useState<ActiveLines>(initialActiveLines);

	// Allow null for mixed states or when uninitialized by explicit user action
	const [borderWidthPt, setBorderWidthPt] = useState<number | null>(1);
	const [borderColor, setBorderColor] = useState<string | null>(
		globalBorderColor || "#000000",
	);

	const [userHasModifiedBorderWidth, setUserHasModifiedBorderWidth] =
		useState(false);
	const [userHasModifiedBorderColor, setUserHasModifiedBorderColor] =
		useState(false);

	const isMultiCellSelection = !!(
		selection &&
		(selection.start.row !== selection.end.row ||
			selection.start.col !== selection.end.col)
	);

	const prevSelectedElementIdRef = useRef<string | undefined | null>(null);
	const prevIsMultiCellSelectionRef = useRef<boolean | undefined>(undefined);

	useEffect(() => {
		const currentSelection = selection;
		const currentCells = tableCells;
		const currentIsMultiCellSelection = !!(
			currentSelection &&
			(currentSelection.start.row !== currentSelection.end.row ||
				currentSelection.start.col !== currentSelection.end.col)
		);
		const hasSelectedElementChanged =
			selectedElementId !== prevSelectedElementIdRef.current;
		const hasMultiCellStatusChanged =
			selectedElementId === prevSelectedElementIdRef.current &&
			currentIsMultiCellSelection !== prevIsMultiCellSelectionRef.current;

		if (hasSelectedElementChanged || hasMultiCellStatusChanged) {
			setUserHasModifiedBorderWidth(false);
			setUserHasModifiedBorderColor(false);

			// Start with no borders selected
			setActiveLines(initialActiveLines);

			let newBorderWidthPt: number | null = 1;
			let newBorderColorHex: string | null = globalBorderColor || "#000000";

			if (currentSelection && currentCells && currentCells.length > 0) {
				const R_START = Math.min(
					currentSelection.start.row,
					currentSelection.end.row,
				);
				const R_END = Math.max(
					currentSelection.start.row,
					currentSelection.end.row,
				);
				const C_START = Math.min(
					currentSelection.start.col,
					currentSelection.end.col,
				);
				const C_END = Math.max(
					currentSelection.start.col,
					currentSelection.end.col,
				);

				let firstWidth: number | undefined;
				let firstColor: string | null | undefined;
				let widthIsConsistent = true;
				let colorIsConsistent = true;
				let foundFirstRelevantBorder = false;

				// Create logical grid to properly handle rowspan/colspan cells
				// We need to use the full table dimensions, not just the selection
				const tableRows = Math.max(...currentCells.map((_, idx) => idx + 1));
				const tableCols = Math.max(
					...currentCells.map((row) =>
						row.reduce((sum, cell) => sum + cell.colspan, 0),
					),
				);
				const logicalGrid = createLogicalGrid(
					currentCells,
					tableRows,
					tableCols,
				);

				// Track which actual cells we've already processed to avoid duplicates
				const processedCells = new Set<string>();

				for (let r = R_START; r <= R_END; r++) {
					for (let c = C_START; c <= C_END; c++) {
						const gridCell = logicalGrid[r]?.[c];
						if (!gridCell) continue;

						const { rowIndex, colIndex } = gridCell;
						const cellKey = `${rowIndex}-${colIndex}`;
						if (processedCells.has(cellKey)) continue;
						processedCells.add(cellKey);

						if (
							rowIndex >= currentCells.length ||
							colIndex >= currentCells[rowIndex].length
						)
							continue;
						const cell = currentCells[rowIndex][colIndex];
						if (!cell?.borderSettings) continue;

						const settings = cell.borderSettings;
						const bordersToCheck: (TableCellBorder | undefined)[] = [];

						// Find the logical position of this cell
						let cellLogicalRowStart = -1;
						let cellLogicalColStart = -1;
						for (let lr = 0; lr < logicalGrid.length; lr++) {
							for (let lc = 0; lc < logicalGrid[lr].length; lc++) {
								const ref = logicalGrid[lr][lc];
								if (ref?.rowIndex === rowIndex && ref?.colIndex === colIndex) {
									if (cellLogicalRowStart === -1) {
										cellLogicalRowStart = lr;
										cellLogicalColStart = lc;
									}
								}
							}
						}

						if (cellLogicalRowStart === -1 || cellLogicalColStart === -1)
							continue;

						const cellLogicalRowEnd = cellLogicalRowStart + cell.rowspan - 1;
						const cellLogicalColEnd = cellLogicalColStart + cell.colspan - 1;

						// Check borders based on logical position within selection
						if (cellLogicalRowStart === R_START)
							bordersToCheck.push(settings.top);
						if (cellLogicalRowEnd === R_END)
							bordersToCheck.push(settings.bottom);
						if (cellLogicalColStart === C_START)
							bordersToCheck.push(settings.left);
						if (cellLogicalColEnd === C_END)
							bordersToCheck.push(settings.right);
						if (currentIsMultiCellSelection) {
							if (cellLogicalRowEnd < R_END)
								bordersToCheck.push(settings.bottom);
							if (cellLogicalColEnd < C_END)
								bordersToCheck.push(settings.right);
						}

						for (const border of bordersToCheck) {
							if (border && border.width > 0) {
								if (!foundFirstRelevantBorder) {
									firstWidth = border.width;
									firstColor = border.color;
									foundFirstRelevantBorder = true;
								} else {
									if (border.width !== firstWidth) widthIsConsistent = false;
									if (border.color !== firstColor) colorIsConsistent = false;
								}
							}
							if (!widthIsConsistent && !colorIsConsistent) break;
						}
						if (!widthIsConsistent && !colorIsConsistent) break;
					}
					if (!widthIsConsistent && !colorIsConsistent) break;
				}

				if (foundFirstRelevantBorder) {
					newBorderWidthPt =
						widthIsConsistent && firstWidth !== undefined
							? pxToPt(firstWidth)
							: null;
					newBorderColorHex =
						colorIsConsistent && firstColor !== undefined ? firstColor : null;
				} else {
					newBorderWidthPt = null;
					newBorderColorHex = null;
				}
			} else {
				newBorderWidthPt = 1;
				newBorderColorHex = globalBorderColor || "#000000";
			}
			setBorderWidthPt(newBorderWidthPt);
			setBorderColor(newBorderColorHex);
		}

		prevSelectedElementIdRef.current = selectedElementId;
		prevIsMultiCellSelectionRef.current = currentIsMultiCellSelection;
	}, [selectedElementId, selection, tableCells, globalBorderColor]);

	const stableOnUpdateCellBorders = useCallback(onUpdateCellBorders, []);

	// Helper function to check if all actively selected border lines have the same width
	const getConsistentBorderWidth = useCallback(
		(checkActiveLines: ActiveLines) => {
			if (!selection || !tableCells || tableCells.length === 0) return null;

			const R_START = Math.min(selection.start.row, selection.end.row);
			const R_END = Math.max(selection.start.row, selection.end.row);
			const C_START = Math.min(selection.start.col, selection.end.col);
			const C_END = Math.max(selection.start.col, selection.end.col);

			let firstWidth: number | undefined;
			let allWidthsConsistent = true;
			let foundActiveLineWithBorder = false;

			// Create logical grid for consistent border checking
			const tableRows = Math.max(...tableCells.map((_, idx) => idx + 1));
			const tableCols = Math.max(
				...tableCells.map((row) =>
					row.reduce((sum, cell) => sum + cell.colspan, 0),
				),
			);
			const logicalGrid = createLogicalGrid(tableCells, tableRows, tableCols);
			const processedCells = new Set<string>();

			// Check widths of actively selected lines
			for (let r = R_START; r <= R_END && allWidthsConsistent; r++) {
				for (let c = C_START; c <= C_END && allWidthsConsistent; c++) {
					const gridCell = logicalGrid[r]?.[c];
					if (!gridCell) continue;

					const { rowIndex, colIndex } = gridCell;
					const cellKey = `${rowIndex}-${colIndex}`;
					if (processedCells.has(cellKey)) continue;
					processedCells.add(cellKey);

					if (
						rowIndex >= tableCells.length ||
						colIndex >= tableCells[rowIndex].length
					)
						continue;
					const cell = tableCells[rowIndex][colIndex];
					if (!cell?.borderSettings) continue;

					const settings = cell.borderSettings;

					// Find logical position of this cell
					let cellLogicalRowStart = -1;
					let cellLogicalColStart = -1;
					for (let lr = 0; lr < logicalGrid.length; lr++) {
						for (let lc = 0; lc < logicalGrid[lr].length; lc++) {
							const ref = logicalGrid[lr][lc];
							if (ref?.rowIndex === rowIndex && ref?.colIndex === colIndex) {
								if (cellLogicalRowStart === -1) {
									cellLogicalRowStart = lr;
									cellLogicalColStart = lc;
								}
							}
						}
					}

					if (cellLogicalRowStart === -1 || cellLogicalColStart === -1)
						continue;

					const cellLogicalRowEnd = cellLogicalRowStart + cell.rowspan - 1;
					const cellLogicalColEnd = cellLogicalColStart + cell.colspan - 1;

					// Check outer top
					if (
						cellLogicalRowStart === R_START &&
						checkActiveLines.outerTop &&
						settings.top
					) {
						if (!foundActiveLineWithBorder) {
							firstWidth = settings.top.width;
							foundActiveLineWithBorder = true;
						} else if (settings.top.width !== firstWidth) {
							allWidthsConsistent = false;
						}
					}

					// Check outer bottom
					if (
						cellLogicalRowEnd === R_END &&
						checkActiveLines.outerBottom &&
						settings.bottom
					) {
						if (!foundActiveLineWithBorder) {
							firstWidth = settings.bottom.width;
							foundActiveLineWithBorder = true;
						} else if (settings.bottom.width !== firstWidth) {
							allWidthsConsistent = false;
						}
					}

					// Check outer left
					if (
						cellLogicalColStart === C_START &&
						checkActiveLines.outerLeft &&
						settings.left
					) {
						if (!foundActiveLineWithBorder) {
							firstWidth = settings.left.width;
							foundActiveLineWithBorder = true;
						} else if (settings.left.width !== firstWidth) {
							allWidthsConsistent = false;
						}
					}

					// Check outer right
					if (
						cellLogicalColEnd === C_END &&
						checkActiveLines.outerRight &&
						settings.right
					) {
						if (!foundActiveLineWithBorder) {
							firstWidth = settings.right.width;
							foundActiveLineWithBorder = true;
						} else if (settings.right.width !== firstWidth) {
							allWidthsConsistent = false;
						}
					}

					// Check inner horizontal
					if (
						cellLogicalRowEnd < R_END &&
						checkActiveLines.innerHorizontal &&
						settings.bottom
					) {
						if (!foundActiveLineWithBorder) {
							firstWidth = settings.bottom.width;
							foundActiveLineWithBorder = true;
						} else if (settings.bottom.width !== firstWidth) {
							allWidthsConsistent = false;
						}
					}

					// Check inner vertical
					if (
						cellLogicalColEnd < C_END &&
						checkActiveLines.innerVertical &&
						settings.right
					) {
						if (!foundActiveLineWithBorder) {
							firstWidth = settings.right.width;
							foundActiveLineWithBorder = true;
						} else if (settings.right.width !== firstWidth) {
							allWidthsConsistent = false;
						}
					}
				}
			}

			return foundActiveLineWithBorder &&
				allWidthsConsistent &&
				firstWidth !== undefined
				? pxToPt(firstWidth)
				: null;
		},
		[selection, tableCells],
	);

	if (!selection) {
		return null;
	}

	const handleToggleLine = (line: keyof ActiveLines) => {
		const newActiveLines = { ...activeLines, [line]: !activeLines[line] };
		setActiveLines(newActiveLines);

		// Check if the newly selected lines have a consistent width
		const consistentWidth = getConsistentBorderWidth(newActiveLines);

		let finalBorderWidthPt = borderWidthPt;
		let finalBorderColor = borderColor;

		// Initialize width and color for toggled lines
		if (!userHasModifiedBorderWidth) {
			if (consistentWidth !== null) {
				// Use the consistent width from selected lines
				finalBorderWidthPt = consistentWidth;
				setBorderWidthPt(consistentWidth);
			} else {
				// Use default width
				const defaultWidthPt = pxToPt(tableProperties.borderWidth);
				finalBorderWidthPt = defaultWidthPt;
				setBorderWidthPt(defaultWidthPt);
			}
			setUserHasModifiedBorderWidth(true);
		}
		if (!userHasModifiedBorderColor) {
			const defaultColorHex = tableProperties.borderColor || "#000000";
			finalBorderColor = defaultColorHex;
			setBorderColor(defaultColorHex);
			setUserHasModifiedBorderColor(true);
		}

		// Apply changes immediately when lines are toggled
		if (
			selection &&
			(newActiveLines.outerTop ||
				newActiveLines.outerBottom ||
				newActiveLines.outerLeft ||
				newActiveLines.outerRight ||
				newActiveLines.innerHorizontal ||
				newActiveLines.innerVertical)
		) {
			stableOnUpdateCellBorders(
				selection,
				newActiveLines,
				finalBorderWidthPt,
				finalBorderColor,
			);
		}
	};

	const handleBorderWidthChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value = e.target.value;
		let newWidthPt: number | null;

		if (value === "") {
			newWidthPt = null;
		} else {
			const numValue = parseFloat(value);
			// Allow 0 to disable borders
			newWidthPt = numValue >= 0 ? numValue : 0;
		}

		setBorderWidthPt(newWidthPt);

		// Apply changes immediately only if lines are selected
		if (
			selection &&
			(activeLines.outerTop ||
				activeLines.outerBottom ||
				activeLines.outerLeft ||
				activeLines.outerRight ||
				activeLines.innerHorizontal ||
				activeLines.innerVertical)
		) {
			stableOnUpdateCellBorders(
				selection,
				activeLines,
				newWidthPt,
				borderColor,
			);
		}
	};

	const handleBorderColorChange = (newColor: string) => {
		setBorderColor(newColor);

		// Apply changes immediately only if lines are selected
		if (
			selection &&
			(activeLines.outerTop ||
				activeLines.outerBottom ||
				activeLines.outerLeft ||
				activeLines.outerRight ||
				activeLines.innerHorizontal ||
				activeLines.innerVertical)
		) {
			stableOnUpdateCellBorders(
				selection,
				activeLines,
				borderWidthPt,
				newColor,
			);
		}
	};

	const lineBaseClass =
		"absolute cursor-pointer transition-colors duration-150 ease-in-out";

	return (
		<div className="space-y-3 p-2 border rounded-md bg-slate-50">
			<Label className="text-sm font-medium text-center block">
				Rahmen bearbeiten
			</Label>
			<div className="text-xs text-gray-600 text-center px-1">
				Der zuletzt gesetzte Rahmen überschreibt vorherige Einstellungen
				zwischen Zellen.
			</div>

			<div className="space-y-2 text-center">
				<Label className="text-xs">Linien auswählen:</Label>
				<div className="relative w-16 h-16 mx-auto my-2 bg-white border border-slate-200 rounded shadow-sm">
					<button
						type="button"
						title="Obere Außenlinie"
						className={`${lineBaseClass} top-0 left-0 w-full h-2 border-none ${activeLines.outerTop ? "bg-blue-500" : "bg-slate-300 hover:bg-slate-400"}`}
						onClick={() => handleToggleLine("outerTop")}
					/>
					<button
						type="button"
						title="Untere Außenlinie"
						className={`${lineBaseClass} bottom-0 left-0 w-full h-2 border-none ${activeLines.outerBottom ? "bg-blue-500" : "bg-slate-300 hover:bg-slate-400"}`}
						onClick={() => handleToggleLine("outerBottom")}
					/>
					<button
						type="button"
						title="Linke Außenlinie"
						className={`${lineBaseClass} top-0 left-0 w-2 h-full border-none ${activeLines.outerLeft ? "bg-blue-500" : "bg-slate-300 hover:bg-slate-400"}`}
						onClick={() => handleToggleLine("outerLeft")}
					/>
					<button
						type="button"
						title="Rechte Außenlinie"
						className={`${lineBaseClass} top-0 right-0 w-2 h-full border-none ${activeLines.outerRight ? "bg-blue-500" : "bg-slate-300 hover:bg-slate-400"}`}
						onClick={() => handleToggleLine("outerRight")}
					/>

					{isMultiCellSelection && (
						<>
							<button
								type="button"
								title="Innere vertikale Linien"
								className={`${lineBaseClass} left-1/2 transform -translate-x-1/2 w-2 h-[calc(100%-16px)] top-2 border-none ${activeLines.innerVertical ? "bg-blue-500" : "bg-slate-300 hover:bg-slate-400"}`}
								onClick={() => handleToggleLine("innerVertical")}
							/>
							<button
								type="button"
								title="Innere horizontale Linien"
								className={`${lineBaseClass} top-1/2 transform -translate-y-1/2 h-2 w-[calc(100%-16px)] left-2 border-none ${activeLines.innerHorizontal ? "bg-blue-500" : "bg-slate-300 hover:bg-slate-400"}`}
								onClick={() => handleToggleLine("innerHorizontal")}
							/>
						</>
					)}
				</div>
			</div>

			<div className="space-y-2 px-1">
				<Label htmlFor="borderWidthPt" className="text-xs font-normal">
					Stärke (pt)
				</Label>
				<Input
					id="borderWidthPt"
					type="number"
					value={borderWidthPt === null ? "" : borderWidthPt}
					onChange={handleBorderWidthChange}
					placeholder={borderWidthPt === null ? "Mixed" : "1"}
					min="0"
					step="0.1"
					className="h-8 w-full"
				/>
			</div>
			<div className="space-y-1 px-1">
				<Label className="text-xs font-normal">Farbe</Label>
				<ColorPicker
					label=""
					currentColor={borderColor === null ? undefined : borderColor}
					onColorChange={handleBorderColorChange}
					apiColors={apiColors}
					isLoadingColors={isLoadingColors}
					colorError={colorError}
				/>
			</div>
		</div>
	);
};
